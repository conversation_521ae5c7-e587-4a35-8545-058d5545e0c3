<template>
  <div>
    <div v-if="!tableInfo4rule" class="app-container">
      <!-- tag区域 -->
      <div class="tag-box">
        <el-tag
          v-for="tag in tags"
          :key="tag.name"
          effect="plain"
          :closable="tag.close"
          :type="tag.id===currentTagId?'':'info'"
          size="small"
          @click="clickTag(tag)"
          @close="closeTag(tag)"
        >
          {{ tag.name }}
        </el-tag>
      </div>
      <!-- 数据列表 -->
      <div v-show="currentTagId === 0">
        <div class="top-search-area">
          <el-form ref="queryForm" size="small" :model="queryParams" :inline="true">
            <el-form-item label="名称" prop="tableName">
              <el-input
                v-model="queryParams.tableName"
                placeholder="请输入表格名称"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <div class="main-btn">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="addRootGroup"
            >新增分组</el-button>
          </div>
        </div>
        <table-list
          ref="treeTable"
          :dict-type="dictType"
          :table-data="tableData"
          :loading="listLoading"
          @refreshTable="getList"
          @addChildGroup="openChildGroup"
          @openTableDetail="openTableDetail"
          @merge="mergeCallBack"
          @openRule="openRule"
        />
      </div>
      <div v-if="currentTagId !== 0">
        <table-detail :table-permission="tablePermission" :dict-type="dictType" :cur-table-detail="curTableDetail" :cube-flow-row="cubeFlowRow" @changeTable="changeTable" />
      </div>
      <add-group v-if="addGroupVisible" :current-node-id="currentNodeId" @close="closeAddGroup" @handleAddGroup="handleAddGroup" />
    </div>
    <calcTable v-else :dim-table-info="tableInfo4rule" @closeRule="closeRule" />
  </div>
</template>

<script>
import { addDimTable, getList } from '@/api/data-cube'
import tableList from './components/table-list.vue'
import addGroup from './components/add-group.vue'
import tableDetail from './components/table-detail.vue'
import calcTable from './components/calculateRule/calcRule.vue'

export default {
  components: { tableList, addGroup, tableDetail, calcTable },
  data() {
    return {
      tags: [
        { name: '数方列表', id: 0, close: false }
      ],
      lastTagId: 0,
      currentTagId: 0,
      queryParams: {
        tableName: undefined
      },
      total: 0,
      tableData: [],
      listLoading: false,
      addGroupVisible: false,
      curTableDetail: {},
      dictType: [],
      timer: null,
      permissionArr: this.$store.getters.tablePermissions,
      tablePermission: null,
      tableInfo4rule: null,
      cubeFlowRow: {}
    }
  },
  watch: {
    cubeFlowRow: {
      handler: function(row) {
        const { id, tableId, tableName } = row
        if (tableId && tableName) {
          this.openTableDetail({ id: tableId, tableName: tableName, taskId: id })
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    this.getList()
    this.cubeFlowRow = this.$route.params
  },
  methods: {
    // 表格详情页保存操作
    changeTable(data) {
      this.curTableDetail = data
    },
    openChildGroup(row) {
      this.currentNodeId = row.id
      this.addGroupVisible = true
    },
    addRootGroup() {
      this.currentNodeId = ''
      this.addGroupVisible = true
    },
    // 新增分组
    handleAddGroup(form) {
      const params = this.currentNodeId ? {
        tableName: form.name,
        parentId: this.currentNodeId,
        type: 'GROUP'
      } : {
        tableName: form.name,
        type: 'GROUP'
      }
      addDimTable(params).then(res => {
        if (res.code === 200) {
          this.$message.success('新增成功')
          this.closeAddGroup()
          this.getList(res.data)
        }
      })
    },
    // 关闭分组弹框
    closeAddGroup() {
      this.addGroupVisible = false
    },
    /** 查询按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.searchForm.tableName = ''
      this.handleQuery()
    },
    // 获取表格数据
    getList(targetId) {
      this.listLoading = true
      getList(this.queryParams).then(res => {
        if (res.code === 200) {
          this.tableData = res.data
          this.total = res.total
          // 如果是新增则展开刚才新增的表格数据
          if (targetId) {
            this.loopExpandTree(targetId)
            // 刷新表格权限
            this.refreshPermission()
          }
          // if (this.tableData.length) {
          //   this.getTableProcess()
          //   this.loopGetProcess()
          // }
        }
      }).finally(() => {
        this.listLoading = false
      })
    },
    async  mergeCallBack() {
      await this.refreshPermission()
      this.getList()
    },
    async refreshPermission() {
      await this.$store.dispatch('user/getInfo')
      this.permissionArr = this.$store.getters.tablePermissions
    },
    // 需要循环遍历该节点上的所有父节点并一一展开
    loopExpandTree(id) {
      const addRow = this.tableData.find(item => item.id === id)
      if (addRow && addRow.parentId !== 0) {
        const expandRow = this.tableData.find(item => item.id === addRow.parentId)
        if (expandRow && expandRow.id) {
          if (this.$refs.treeTable) {
            // this.$refs.treeTable.setAllTreeExpand(false)
            this.$refs.treeTable.expandTree(expandRow)
          }
          this.loopExpandTree(expandRow.id)
        }
      } else {
        this.$refs.treeTable.expandTree(addRow)
      }
      this.$refs.treeTable.setCurrentRow(addRow)
    },
    // 点击tag
    clickTag(tag) {
      if (tag.id !== 0) {
        this.lastTagId = tag.id
      }
      this.currentTagId = tag.id
      this.curTableDetail = this.tableData.find(item => item.id === tag.id) || {}
      if (tag.id === 0) {
        this.loopExpandTree(this.lastTagId)
      }
    },
    closeTag(tag) {
      // 如果关闭的tag在列表中高亮则先清除列表高中状态
      if (tag.id === this.lastTagId) {
        this.$refs.treeTable.clearCurrentRow()
      }
      this.tags = this.tags.filter((item) => item.id !== tag.id)
      if (this.currentTagId !== 0) {
        this.currentTagId = this.tags.length === 1 ? 0 : this.tags[1].id
      }
    },
    // 打开数据表(打开一个表新开一个tab)
    openTableDetail(row) {
      console.log(this.permissionArr)
      const { id, tableName } = row
      this.tablePermission = this.permissionArr.filter(item => {
        return item.tableId === String(id)
      })[0] || null
      console.log(this.tablePermission)
      const name = tableName
      const len = this.tags.length
      const target = this.tags.find(item => item.id === id)
      if (len === 1 || !target) {
        this.tags.push({ name, id, close: true })
      }
      this.currentTagId = id
      this.lastTagId = id
      this.curTableDetail = row
    },
    openRule(row) {
      this.tableInfo4rule = row
    },
    closeRule() {
      const highlightRow = { ...this.tableInfo4rule }
      this.tableInfo4rule = null
      this.$nextTick(() => {
        if (this.$refs.treeTable) {
          this.loopExpandTree(highlightRow.id)
          this.$refs.treeTable.setCurrentRow(highlightRow)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import  '@/styles/variables';
.search-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0 15px 0;
}
.search-form {
  .el-form-item {
    margin-bottom: 0;
  }
}
.tag-box {
  margin-bottom:10px;
}
</style>
