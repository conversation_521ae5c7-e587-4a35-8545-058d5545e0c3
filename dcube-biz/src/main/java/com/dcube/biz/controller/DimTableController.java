package com.dcube.biz.controller;

import com.dcube.biz.dto.DimTableDataDto;
import com.dcube.biz.dto.DimTableDto;
import com.dcube.biz.dto.LayoutDto;
import com.dcube.biz.query.DimTableDataQuery;
import com.dcube.biz.query.DimTableListQuery;
import com.dcube.biz.service.IDimTableService;
import com.dcube.common.annotation.Log;
import com.dcube.common.core.controller.BaseController;
import com.dcube.common.core.domain.AjaxResult;
import com.dcube.common.enums.BusinessType;
import com.dcube.cube.spi.CubeSchema;
import com.dcube.cube.spi.CubeServer;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("dim_table")
@Tag(name = "DCUBE-多维表", description = "DCUBE-多维表")
public class DimTableController extends BaseController {

    @Autowired
    private IDimTableService dimTableService;
//    @Autowired
//    private IDimRuleServiceHelper dimRuleServiceHelper;

    @GetMapping("list")
    @Operation(summary = "列表(全量)")
    public AjaxResult list(DimTableListQuery query) {
        return AjaxResult.success(dimTableService.queryList(query));
    }

    /**
     * 主键查询
     */
    @GetMapping("{id}")
    @Operation(summary = "主键查询", description = "")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return AjaxResult.success(dimTableService.getByIdExt(id, getUserId()));
    }

    /**
     * 新增
     */
    @PostMapping("add")
    @Operation(summary = "新增", description = "")
    @Log(title = "新增多维表", businessType = BusinessType.INSERT)
    public AjaxResult add(@RequestBody DimTableDto dto) {
        dto.setId(null);
        return AjaxResult.success(dimTableService.saveExt(dto));
    }

    /**
     * 修改
     */
    @PostMapping("put")
    @Operation(summary = "修改", description = "")
    @Log(title = "修改多维表", businessType = BusinessType.UPDATE)
    public AjaxResult edit(@RequestBody DimTableDto dto) {
        return AjaxResult.success(dimTableService.updateExt(dto));
    }

    /**
     * 数组删除
     */
    @PostMapping("remove/{ids}")
    @Operation(summary = "数组删除", description = "")
    @Log(title = "删除多维表", businessType = BusinessType.DELETE)
    public AjaxResult remove(@PathVariable Integer[] ids) {
        dimTableService.removeByIds(ids);
        return AjaxResult.success();
    }

    @PostMapping("loading")
    @Operation(summary = "加载多维表数据")
    public AjaxResult loading(@RequestParam("id") Integer id) {
        dimTableService.loading(id);
        return AjaxResult.success();
    }

    @PostMapping("listTableData")
    @Operation(summary = "查询多维表数据")
    public AjaxResult listTableData(@RequestBody DimTableDataQuery query) {
        return AjaxResult.success(dimTableService.listTableData(query));
    }

    @PostMapping("addData")
    @Operation(summary = "新增多维表数据")
    public AjaxResult addData(@RequestBody DimTableDataDto dimTableDataDto) {
        dimTableService.addData(dimTableDataDto);
//        // 触发计算规则
//        dimRuleServiceHelper.execute(dimTableDataDto.getTableId());
        return AjaxResult.success();
    }

    @PostMapping("layout")
    @Operation(summary = "布局保存")
    @Log(title = "布局保存", businessType = BusinessType.INSERT)
    public AjaxResult layout(@RequestBody LayoutDto layoutDto) {
        layoutDto.setUserId(getUserId());
        dimTableService.layout(layoutDto);
        return AjaxResult.success();
    }

    @GetMapping("test")
    @Operation(summary = "testing")
    public AjaxResult test(@RequestParam("id") String id) {
        CubeServer c = CubeSchema.get().getTable(id);
        return AjaxResult.success();
    }

    @PostMapping("backup")
    @Operation(summary = "备份到数据库")
    @Log(title = "备份到数据库", businessType = BusinessType.INSERT)
    public AjaxResult backup(@RequestParam("id") Integer id) {
        dimTableService.backup(id);
        return AjaxResult.success();
    }

    /**
     * 重命名
     */
    @PostMapping("rename")
    @Operation(summary = "重命名", description = "")
    @Log(title = "重命名", businessType = BusinessType.UPDATE)
    public AjaxResult rename(@RequestBody DimTableDto dto) {
        return AjaxResult.success(dimTableService.rename(dto));
    }

}
